package com.example.jobrec.activities

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.jobrec.Job
import com.example.jobrec.JobDetailsActivity
import com.example.jobrec.R
import com.example.jobrec.adapters.PaginatedJobsAdapter
import com.example.jobrec.utils.*
import com.google.android.material.textfield.TextInputEditText
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.launch

/**
 * Enhanced JobsActivity with pagination, caching, and performance optimizations
 */
class EnhancedJobsActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "EnhancedJobsActivity"
        private const val CACHE_KEY_ALL_JOBS = "all_jobs"
        private const val CACHE_KEY_FILTERED_JOBS = "filtered_jobs"
    }
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var searchEditText: EditText
    private lateinit var fieldFilterInput: TextInputEditText
    private lateinit var emptyStateLayout: View
    private lateinit var progressBar: View
    
    private lateinit var jobsAdapter: PaginatedJobsAdapter
    private lateinit var cacheManager: CacheManager
    private lateinit var paginationHelper: PaginationHelper<Job>
    private lateinit var loadingStateManager: PerformanceUtils.LoadingStateManager
    private lateinit var searchDebouncer: PerformanceUtils.Debouncer
    
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    private var currentSearchQuery = ""
    private var currentFieldFilter = ""
    private var isInitialLoad = true
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_jobs)
        
        val timer = PerformanceUtils.startTimer("JobsActivity onCreate")
        
        initializeComponents()
        setupRecyclerView()
        setupSwipeRefresh()
        setupSearch()
        loadInitialData()
        
        timer.end()
    }
    
    private fun initializeComponents() {
        recyclerView = findViewById(R.id.jobsRecyclerView)
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout)
        searchEditText = findViewById(R.id.searchEditText)
        fieldFilterInput = findViewById(R.id.fieldFilterInput)
        emptyStateLayout = findViewById(R.id.emptyStateLayout)
        progressBar = findViewById(R.id.progressBar)
        
        // Initialize utilities
        cacheManager = CacheManager.getInstance(this)
        searchDebouncer = PerformanceUtils.Debouncer(300L, lifecycleScope)
        
        loadingStateManager = PerformanceUtils.LoadingStateManager(
            progressBar = progressBar,
            contentView = recyclerView,
            emptyStateView = emptyStateLayout,
            swipeRefreshLayout = swipeRefreshLayout
        )
        
        paginationHelper = PaginationHelperFactory.createJobsPaginationHelper(
            pageSize = 20,
            cacheKey = CACHE_KEY_ALL_JOBS,
            cacheManager = cacheManager
        )
    }
    
    private fun setupRecyclerView() {
        jobsAdapter = PaginatedJobsAdapter(
            onJobClick = { job -> openJobDetails(job) },
            onLoadMore = { loadMoreJobs() }
        )
        
        val layoutManager = LinearLayoutManager(this)
        recyclerView.layoutManager = layoutManager
        recyclerView.adapter = jobsAdapter
        
        // Optimize RecyclerView performance
        PerformanceUtils.RecyclerViewOptimizer.optimizeRecyclerView(recyclerView)
        
        Log.d(TAG, "RecyclerView setup completed")
    }
    
    private fun setupSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener {
            refreshJobs()
        }
        swipeRefreshLayout.setColorSchemeResources(
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        )
    }
    
    private fun setupSearch() {
        searchEditText.setOnEditorActionListener { _, _, _ ->
            performSearch()
            true
        }
        
        // Add text change listener with debouncing
        searchEditText.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                searchDebouncer.debounce {
                    performSearch()
                }
            }
        })
        
        fieldFilterInput.setOnEditorActionListener { _, _, _ ->
            performSearch()
            true
        }
    }
    
    private fun loadInitialData() {
        if (isInitialLoad) {
            loadingStateManager.showLoading()
            isInitialLoad = false
        }
        
        lifecycleScope.launch {
            try {
                val timer = PerformanceUtils.startTimer("Initial jobs load")
                
                val query = buildJobsQuery()
                val result = paginationHelper.loadFirstPage(query)
                
                handleJobsResult(result, isRefresh = false)
                timer.end()
                
                // Prefetch images for better UX
                val imageUrls = result.items.mapNotNull { job ->
                    // Add any company logo URLs if available
                    null // Placeholder for company logos
                }.filterNotNull()
                
                if (imageUrls.isNotEmpty()) {
                    PerformanceUtils.PrefetchUtils.prefetchImages(this@EnhancedJobsActivity, imageUrls)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading initial data", e)
                loadingStateManager.showEmpty()
                Toast.makeText(this@EnhancedJobsActivity, "Error loading jobs: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun loadMoreJobs() {
        if (jobsAdapter.isCurrentlyLoading()) return
        
        lifecycleScope.launch {
            try {
                jobsAdapter.setLoadingState(true)
                
                val query = buildJobsQuery()
                val result = paginationHelper.loadNextPage(query)
                
                handleJobsResult(result, isRefresh = false)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading more jobs", e)
                jobsAdapter.setLoadingState(false)
                Toast.makeText(this@EnhancedJobsActivity, "Error loading more jobs", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun refreshJobs() {
        lifecycleScope.launch {
            try {
                val timer = PerformanceUtils.startTimer("Jobs refresh")
                
                val query = buildJobsQuery()
                val result = paginationHelper.refresh(query)
                
                handleJobsResult(result, isRefresh = true)
                timer.end()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing jobs", e)
                loadingStateManager.hideLoading()
                Toast.makeText(this@EnhancedJobsActivity, "Error refreshing jobs: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun performSearch() {
        val searchQuery = searchEditText.text.toString().trim()
        val fieldFilter = fieldFilterInput.text.toString().trim()
        
        if (searchQuery == currentSearchQuery && fieldFilter == currentFieldFilter) {
            return // No change in search criteria
        }
        
        currentSearchQuery = searchQuery
        currentFieldFilter = fieldFilter
        
        // Reset pagination for new search
        paginationHelper.reset()
        
        // Update cache key for filtered results
        val newCacheKey = if (searchQuery.isNotEmpty() || fieldFilter.isNotEmpty()) {
            "${CACHE_KEY_FILTERED_JOBS}_${searchQuery}_${fieldFilter}"
        } else {
            CACHE_KEY_ALL_JOBS
        }
        
        lifecycleScope.launch {
            try {
                loadingStateManager.showLoading()
                
                val query = buildJobsQuery()
                val result = paginationHelper.loadFirstPage(query)
                
                handleJobsResult(result, isRefresh = true)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error performing search", e)
                loadingStateManager.showEmpty()
                Toast.makeText(this@EnhancedJobsActivity, "Error searching jobs: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun buildJobsQuery(): Query {
        var query = db.collection("jobs")
            .whereEqualTo("status", "active")
            .orderBy("postedDate", Query.Direction.DESCENDING)
        
        // Note: Firestore doesn't support complex text search
        // For production, consider using Algolia or similar service
        // This is a simplified implementation
        
        return query
    }
    
    private fun handleJobsResult(result: PaginationHelper.PaginationResult<Job>, isRefresh: Boolean) {
        if (result.isFirstPage || isRefresh) {
            // First page or refresh - replace all items
            jobsAdapter.submitList(result.items) {
                loadingStateManager.hideLoading()
                if (result.items.isEmpty()) {
                    loadingStateManager.showEmpty()
                } else {
                    loadingStateManager.showContent()
                }
            }
        } else {
            // Additional page - append items
            val currentList = jobsAdapter.currentList.toMutableList()
            currentList.addAll(result.items)
            jobsAdapter.submitList(currentList) {
                jobsAdapter.setLoadingState(false)
            }
        }
        
        jobsAdapter.setHasMoreData(result.hasMore)
        
        Log.d(TAG, "Loaded ${result.items.size} jobs, total: ${result.totalLoaded}, hasMore: ${result.hasMore}")
    }
    
    private fun openJobDetails(job: Job) {
        val intent = Intent(this, JobDetailsActivity::class.java).apply {
            putExtra("jobId", job.id)
            putExtra("job", job)
        }
        startActivity(intent)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        searchDebouncer.cancel()
    }
    
    override fun onResume() {
        super.onResume()
        // Log memory usage for monitoring
        PerformanceUtils.logMemoryUsage(this, TAG)
    }
}
