package com.example.jobrec.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.jobrec.Job
import com.example.jobrec.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Enhanced JobsAdapter with pagination support and performance optimizations
 */
class PaginatedJobsAdapter(
    private val onJobClick: (Job) -> Unit,
    private val onLoadMore: () -> Unit
) : ListAdapter<Job, RecyclerView.ViewHolder>(JobDiffCallback()) {
    
    companion object {
        private const val TYPE_JOB = 0
        private const val TYPE_LOADING = 1
    }
    
    private var isLoading = false
    private var hasMoreData = true
    
    override fun getItemViewType(position: Int): Int {
        return if (position == itemCount - 1 && isLoading) {
            TYPE_LOADING
        } else {
            TYPE_JOB
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_JOB -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_job, parent, false)
                JobViewHolder(view)
            }
            TYPE_LOADING -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_loading, parent, false)
                LoadingViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is JobViewHolder -> {
                val job = getItem(position)
                holder.bind(job)
                
                // Trigger load more when approaching end
                if (position >= itemCount - 3 && hasMoreData && !isLoading) {
                    onLoadMore()
                }
            }
            is LoadingViewHolder -> {
                // Loading item doesn't need binding
            }
        }
    }
    
    override fun getItemCount(): Int {
        val baseCount = super.getItemCount()
        return if (isLoading && hasMoreData) baseCount + 1 else baseCount
    }
    
    fun setLoadingState(loading: Boolean) {
        val wasLoading = isLoading
        isLoading = loading
        
        if (wasLoading != loading) {
            if (loading) {
                notifyItemInserted(itemCount - 1)
            } else {
                notifyItemRemoved(itemCount)
            }
        }
    }
    
    fun setHasMoreData(hasMore: Boolean) {
        hasMoreData = hasMore
        if (!hasMore && isLoading) {
            setLoadingState(false)
        }
    }
    
    inner class JobViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleTextView: TextView = itemView.findViewById(R.id.jobTitleText)
        private val companyTextView: TextView = itemView.findViewById(R.id.companyNameText)
        private val locationTextView: TextView = itemView.findViewById(R.id.locationText)
        private val jobTypeTextView: TextView = itemView.findViewById(R.id.jobTypeText)
        private val salaryTextView: TextView = itemView.findViewById(R.id.salaryText)
        private val postedDateText: TextView = itemView.findViewById(R.id.postedDateText)
        private val matchContainer: LinearLayout = itemView.findViewById(R.id.matchContainer)
        private val matchPercentageText: TextView = itemView.findViewById(R.id.matchPercentageText)
        
        init {
            itemView.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onJobClick(getItem(position))
                }
            }
        }
        
        fun bind(job: Job) {
            titleTextView.text = job.title
            companyTextView.text = job.companyName
            locationTextView.text = job.city
            jobTypeTextView.text = job.type
            salaryTextView.text = job.salary
            
            // Format posted date efficiently
            val postedDate = job.postedDate.toDate()
            val now = Date()
            val diffInMillis = now.time - postedDate.time
            val diffInDays = diffInMillis / (1000 * 60 * 60 * 24)
            
            val dateText = when {
                diffInDays == 0L -> "Posted today"
                diffInDays == 1L -> "Posted yesterday"
                diffInDays < 7 -> "Posted ${diffInDays} days ago"
                diffInDays < 30 -> "Posted ${diffInDays / 7} weeks ago"
                else -> {
                    val dateFormat = SimpleDateFormat("MMM dd", Locale.getDefault())
                    "Posted ${dateFormat.format(postedDate)}"
                }
            }
            postedDateText.text = dateText
            
            // Show match percentage if available
            if (job.matchPercentage > 0) {
                matchContainer.visibility = View.VISIBLE
                matchPercentageText.text = "${job.matchPercentage}%"
                
                val matchColor = when {
                    job.matchPercentage >= 80 -> ContextCompat.getColor(itemView.context, android.R.color.holo_green_dark)
                    job.matchPercentage >= 60 -> ContextCompat.getColor(itemView.context, android.R.color.holo_orange_dark)
                    else -> ContextCompat.getColor(itemView.context, android.R.color.holo_red_dark)
                }
                matchPercentageText.setTextColor(matchColor)
            } else {
                matchContainer.visibility = View.GONE
            }
        }
    }
    
    inner class LoadingViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}

/**
 * Enhanced DiffUtil callback for better performance
 */
class JobDiffCallback : DiffUtil.ItemCallback<Job>() {
    override fun areItemsTheSame(oldItem: Job, newItem: Job): Boolean {
        return oldItem.id == newItem.id
    }
    
    override fun areContentsTheSame(oldItem: Job, newItem: Job): Boolean {
        return oldItem == newItem
    }
    
    override fun getChangePayload(oldItem: Job, newItem: Job): Any? {
        // Return specific payload for partial updates
        val changes = mutableListOf<String>()
        
        if (oldItem.title != newItem.title) changes.add("title")
        if (oldItem.status != newItem.status) changes.add("status")
        if (oldItem.matchPercentage != newItem.matchPercentage) changes.add("match")
        
        return if (changes.isNotEmpty()) changes else null
    }
}

/**
 * Enhanced ApplicationsAdapter with pagination support
 */
class PaginatedApplicationsAdapter(
    private val onApplicationClick: (com.example.jobrec.Application) -> Unit,
    private val onLoadMore: () -> Unit
) : ListAdapter<com.example.jobrec.Application, RecyclerView.ViewHolder>(ApplicationDiffCallback()) {
    
    companion object {
        private const val TYPE_APPLICATION = 0
        private const val TYPE_LOADING = 1
    }
    
    private var isLoading = false
    private var hasMoreData = true
    
    override fun getItemViewType(position: Int): Int {
        return if (position == itemCount - 1 && isLoading) {
            TYPE_LOADING
        } else {
            TYPE_APPLICATION
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_APPLICATION -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_application, parent, false)
                ApplicationViewHolder(view)
            }
            TYPE_LOADING -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_loading, parent, false)
                LoadingViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ApplicationViewHolder -> {
                val application = getItem(position)
                holder.bind(application)
                
                // Trigger load more when approaching end
                if (position >= itemCount - 3 && hasMoreData && !isLoading) {
                    onLoadMore()
                }
            }
            is LoadingViewHolder -> {
                // Loading item doesn't need binding
            }
        }
    }
    
    override fun getItemCount(): Int {
        val baseCount = super.getItemCount()
        return if (isLoading && hasMoreData) baseCount + 1 else baseCount
    }
    
    fun setLoadingState(loading: Boolean) {
        val wasLoading = isLoading
        isLoading = loading
        
        if (wasLoading != loading) {
            if (loading) {
                notifyItemInserted(itemCount - 1)
            } else {
                notifyItemRemoved(itemCount)
            }
        }
    }
    
    fun setHasMoreData(hasMore: Boolean) {
        hasMoreData = hasMore
        if (!hasMore && isLoading) {
            setLoadingState(false)
        }
    }
    
    inner class ApplicationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val jobTitleText: TextView = itemView.findViewById(R.id.jobTitleText)
        private val companyNameText: TextView = itemView.findViewById(R.id.companyNameText)
        private val statusText: TextView = itemView.findViewById(R.id.statusText)
        private val dateText: TextView = itemView.findViewById(R.id.dateText)
        
        init {
            itemView.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onApplicationClick(getItem(position))
                }
            }
        }
        
        fun bind(application: com.example.jobrec.Application) {
            jobTitleText.text = application.jobTitle
            companyNameText.text = application.companyName
            statusText.text = application.status.uppercase()
            
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            dateText.text = dateFormat.format(application.appliedDate.toDate())
            
            // Set status color
            val statusColor = when (application.status.lowercase()) {
                "pending" -> ContextCompat.getColor(itemView.context, android.R.color.holo_orange_dark)
                "reviewing" -> ContextCompat.getColor(itemView.context, android.R.color.holo_blue_dark)
                "shortlisted" -> ContextCompat.getColor(itemView.context, android.R.color.holo_green_dark)
                "rejected" -> ContextCompat.getColor(itemView.context, android.R.color.holo_red_dark)
                else -> ContextCompat.getColor(itemView.context, android.R.color.darker_gray)
            }
            statusText.setTextColor(statusColor)
        }
    }
    
    inner class LoadingViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}

/**
 * DiffUtil callback for applications
 */
class ApplicationDiffCallback : DiffUtil.ItemCallback<com.example.jobrec.Application>() {
    override fun areItemsTheSame(oldItem: com.example.jobrec.Application, newItem: com.example.jobrec.Application): Boolean {
        return oldItem.id == newItem.id
    }
    
    override fun areContentsTheSame(oldItem: com.example.jobrec.Application, newItem: com.example.jobrec.Application): Boolean {
        return oldItem == newItem
    }
}
