package com.example.jobrec.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.jobrec.Application
import com.example.jobrec.Job
import com.example.jobrec.User
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * Comprehensive cache manager for jobs, applications, and user data
 * Provides both in-memory and persistent caching with expiration
 */
class CacheManager private constructor(context: Context) {
    
    companion object {
        private const val TAG = "CacheManager"
        private const val PREFS_NAME = "jobrec_cache"
        private const val CACHE_EXPIRY_TIME = 5 * 60 * 1000L // 5 minutes
        private const val JOBS_CACHE_KEY = "jobs_cache"
        private const val APPLICATIONS_CACHE_KEY = "applications_cache"
        private const val USER_PROFILES_CACHE_KEY = "user_profiles_cache"
        private const val COMPANY_PROFILES_CACHE_KEY = "company_profiles_cache"
        
        @Volatile
        private var INSTANCE: CacheManager? = null
        
        fun getInstance(context: Context): CacheManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CacheManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPrefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    // In-memory caches for fast access
    private val jobsMemoryCache = ConcurrentHashMap<String, CacheEntry<Job>>()
    private val applicationsMemoryCache = ConcurrentHashMap<String, CacheEntry<Application>>()
    private val userProfilesMemoryCache = ConcurrentHashMap<String, CacheEntry<User>>()
    private val companyProfilesMemoryCache = ConcurrentHashMap<String, CacheEntry<Map<String, Any>>>()
    
    // Cache for lists
    private val jobsListCache = ConcurrentHashMap<String, CacheEntry<List<Job>>>()
    private val applicationsListCache = ConcurrentHashMap<String, CacheEntry<List<Application>>>()
    
    data class CacheEntry<T>(
        val data: T,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        fun isExpired(expiryTime: Long = CACHE_EXPIRY_TIME): Boolean {
            return System.currentTimeMillis() - timestamp > expiryTime
        }
    }
    
    // Jobs caching
    suspend fun cacheJob(job: Job) = withContext(Dispatchers.IO) {
        try {
            jobsMemoryCache[job.id] = CacheEntry(job)
            Log.d(TAG, "Cached job: ${job.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching job", e)
        }
    }
    
    suspend fun getJob(jobId: String): Job? = withContext(Dispatchers.IO) {
        try {
            val cacheEntry = jobsMemoryCache[jobId]
            if (cacheEntry != null && !cacheEntry.isExpired()) {
                Log.d(TAG, "Retrieved job from memory cache: $jobId")
                return@withContext cacheEntry.data
            }
            Log.d(TAG, "Job not found in cache or expired: $jobId")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving job from cache", e)
            return@withContext null
        }
    }
    
    suspend fun cacheJobsList(key: String, jobs: List<Job>) = withContext(Dispatchers.IO) {
        try {
            jobsListCache[key] = CacheEntry(jobs)
            // Also cache individual jobs
            jobs.forEach { job ->
                jobsMemoryCache[job.id] = CacheEntry(job)
            }
            
            // Persist to SharedPreferences for longer-term storage
            val jobsJson = gson.toJson(jobs)
            sharedPrefs.edit()
                .putString("${JOBS_CACHE_KEY}_$key", jobsJson)
                .putLong("${JOBS_CACHE_KEY}_${key}_timestamp", System.currentTimeMillis())
                .apply()
            
            Log.d(TAG, "Cached ${jobs.size} jobs with key: $key")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching jobs list", e)
        }
    }
    
    suspend fun getJobsList(key: String): List<Job>? = withContext(Dispatchers.IO) {
        try {
            // Check memory cache first
            val memoryCacheEntry = jobsListCache[key]
            if (memoryCacheEntry != null && !memoryCacheEntry.isExpired()) {
                Log.d(TAG, "Retrieved jobs list from memory cache: $key")
                return@withContext memoryCacheEntry.data
            }
            
            // Check persistent cache
            val timestamp = sharedPrefs.getLong("${JOBS_CACHE_KEY}_${key}_timestamp", 0)
            if (timestamp > 0 && System.currentTimeMillis() - timestamp < CACHE_EXPIRY_TIME) {
                val jobsJson = sharedPrefs.getString("${JOBS_CACHE_KEY}_$key", null)
                if (jobsJson != null) {
                    val type = object : TypeToken<List<Job>>() {}.type
                    val jobs: List<Job> = gson.fromJson(jobsJson, type)
                    
                    // Update memory cache
                    jobsListCache[key] = CacheEntry(jobs)
                    jobs.forEach { job ->
                        jobsMemoryCache[job.id] = CacheEntry(job)
                    }
                    
                    Log.d(TAG, "Retrieved ${jobs.size} jobs from persistent cache: $key")
                    return@withContext jobs
                }
            }
            
            Log.d(TAG, "Jobs list not found in cache or expired: $key")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving jobs list from cache", e)
            return@withContext null
        }
    }
    
    // Applications caching
    suspend fun cacheApplication(application: Application) = withContext(Dispatchers.IO) {
        try {
            applicationsMemoryCache[application.id] = CacheEntry(application)
            Log.d(TAG, "Cached application: ${application.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching application", e)
        }
    }
    
    suspend fun getApplication(applicationId: String): Application? = withContext(Dispatchers.IO) {
        try {
            val cacheEntry = applicationsMemoryCache[applicationId]
            if (cacheEntry != null && !cacheEntry.isExpired()) {
                Log.d(TAG, "Retrieved application from memory cache: $applicationId")
                return@withContext cacheEntry.data
            }
            Log.d(TAG, "Application not found in cache or expired: $applicationId")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving application from cache", e)
            return@withContext null
        }
    }
    
    suspend fun cacheApplicationsList(key: String, applications: List<Application>) = withContext(Dispatchers.IO) {
        try {
            applicationsListCache[key] = CacheEntry(applications)
            // Also cache individual applications
            applications.forEach { application ->
                applicationsMemoryCache[application.id] = CacheEntry(application)
            }
            
            // Persist to SharedPreferences
            val applicationsJson = gson.toJson(applications)
            sharedPrefs.edit()
                .putString("${APPLICATIONS_CACHE_KEY}_$key", applicationsJson)
                .putLong("${APPLICATIONS_CACHE_KEY}_${key}_timestamp", System.currentTimeMillis())
                .apply()
            
            Log.d(TAG, "Cached ${applications.size} applications with key: $key")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching applications list", e)
        }
    }
    
    suspend fun getApplicationsList(key: String): List<Application>? = withContext(Dispatchers.IO) {
        try {
            // Check memory cache first
            val memoryCacheEntry = applicationsListCache[key]
            if (memoryCacheEntry != null && !memoryCacheEntry.isExpired()) {
                Log.d(TAG, "Retrieved applications list from memory cache: $key")
                return@withContext memoryCacheEntry.data
            }
            
            // Check persistent cache
            val timestamp = sharedPrefs.getLong("${APPLICATIONS_CACHE_KEY}_${key}_timestamp", 0)
            if (timestamp > 0 && System.currentTimeMillis() - timestamp < CACHE_EXPIRY_TIME) {
                val applicationsJson = sharedPrefs.getString("${APPLICATIONS_CACHE_KEY}_$key", null)
                if (applicationsJson != null) {
                    val type = object : TypeToken<List<Application>>() {}.type
                    val applications: List<Application> = gson.fromJson(applicationsJson, type)
                    
                    // Update memory cache
                    applicationsListCache[key] = CacheEntry(applications)
                    applications.forEach { application ->
                        applicationsMemoryCache[application.id] = CacheEntry(application)
                    }
                    
                    Log.d(TAG, "Retrieved ${applications.size} applications from persistent cache: $key")
                    return@withContext applications
                }
            }
            
            Log.d(TAG, "Applications list not found in cache or expired: $key")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving applications list from cache", e)
            return@withContext null
        }
    }
    
    // User profiles caching
    suspend fun cacheUserProfile(userId: String, user: User) = withContext(Dispatchers.IO) {
        try {
            userProfilesMemoryCache[userId] = CacheEntry(user)
            
            // Persist to SharedPreferences
            val userJson = gson.toJson(user)
            sharedPrefs.edit()
                .putString("${USER_PROFILES_CACHE_KEY}_$userId", userJson)
                .putLong("${USER_PROFILES_CACHE_KEY}_${userId}_timestamp", System.currentTimeMillis())
                .apply()
            
            Log.d(TAG, "Cached user profile: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching user profile", e)
        }
    }
    
    suspend fun getUserProfile(userId: String): User? = withContext(Dispatchers.IO) {
        try {
            // Check memory cache first
            val memoryCacheEntry = userProfilesMemoryCache[userId]
            if (memoryCacheEntry != null && !memoryCacheEntry.isExpired()) {
                Log.d(TAG, "Retrieved user profile from memory cache: $userId")
                return@withContext memoryCacheEntry.data
            }
            
            // Check persistent cache
            val timestamp = sharedPrefs.getLong("${USER_PROFILES_CACHE_KEY}_${userId}_timestamp", 0)
            if (timestamp > 0 && System.currentTimeMillis() - timestamp < CACHE_EXPIRY_TIME) {
                val userJson = sharedPrefs.getString("${USER_PROFILES_CACHE_KEY}_$userId", null)
                if (userJson != null) {
                    val user: User = gson.fromJson(userJson, User::class.java)
                    
                    // Update memory cache
                    userProfilesMemoryCache[userId] = CacheEntry(user)
                    
                    Log.d(TAG, "Retrieved user profile from persistent cache: $userId")
                    return@withContext user
                }
            }
            
            Log.d(TAG, "User profile not found in cache or expired: $userId")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving user profile from cache", e)
            return@withContext null
        }
    }
    
    // Cache invalidation methods
    suspend fun invalidateJobsCache() = withContext(Dispatchers.IO) {
        try {
            jobsMemoryCache.clear()
            jobsListCache.clear()
            
            // Clear persistent cache
            val editor = sharedPrefs.edit()
            sharedPrefs.all.keys.filter { it.startsWith(JOBS_CACHE_KEY) }.forEach { key ->
                editor.remove(key)
            }
            editor.apply()
            
            Log.d(TAG, "Invalidated jobs cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error invalidating jobs cache", e)
        }
    }
    
    suspend fun invalidateApplicationsCache() = withContext(Dispatchers.IO) {
        try {
            applicationsMemoryCache.clear()
            applicationsListCache.clear()
            
            // Clear persistent cache
            val editor = sharedPrefs.edit()
            sharedPrefs.all.keys.filter { it.startsWith(APPLICATIONS_CACHE_KEY) }.forEach { key ->
                editor.remove(key)
            }
            editor.apply()
            
            Log.d(TAG, "Invalidated applications cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error invalidating applications cache", e)
        }
    }
    
    suspend fun clearAllCache() = withContext(Dispatchers.IO) {
        try {
            jobsMemoryCache.clear()
            applicationsMemoryCache.clear()
            userProfilesMemoryCache.clear()
            companyProfilesMemoryCache.clear()
            jobsListCache.clear()
            applicationsListCache.clear()
            
            sharedPrefs.edit().clear().apply()
            
            Log.d(TAG, "Cleared all cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all cache", e)
        }
    }
}
