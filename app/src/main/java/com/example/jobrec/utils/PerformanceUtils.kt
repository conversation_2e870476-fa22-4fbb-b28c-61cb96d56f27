package com.example.jobrec.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import kotlinx.coroutines.*

/**
 * Performance utilities for monitoring and optimizing app performance
 */
object PerformanceUtils {
    private const val TAG = "PerformanceUtils"
    
    /**
     * Performance timer for measuring operation duration
     */
    class PerformanceTimer(private val operationName: String) {
        private val startTime = System.currentTimeMillis()
        
        fun end(): Long {
            val duration = System.currentTimeMillis() - startTime
            Log.d(TAG, "$operationName completed in ${duration}ms")
            return duration
        }
    }
    
    /**
     * Start a performance timer
     */
    fun startTimer(operationName: String): PerformanceTimer {
        Log.d(TAG, "Starting timer for: $operationName")
        return PerformanceTimer(operationName)
    }
    
    /**
     * Loading state manager for UI components
     */
    class LoadingStateManager(
        private val progressBar: View? = null,
        private val contentView: View? = null,
        private val emptyStateView: View? = null,
        private val swipeRefreshLayout: SwipeRefreshLayout? = null
    ) {
        
        fun showLoading() {
            progressBar?.visibility = View.VISIBLE
            contentView?.visibility = View.GONE
            emptyStateView?.visibility = View.GONE
            swipeRefreshLayout?.isRefreshing = true
        }
        
        fun showContent() {
            progressBar?.visibility = View.GONE
            contentView?.visibility = View.VISIBLE
            emptyStateView?.visibility = View.GONE
            swipeRefreshLayout?.isRefreshing = false
        }
        
        fun showEmpty() {
            progressBar?.visibility = View.GONE
            contentView?.visibility = View.GONE
            emptyStateView?.visibility = View.VISIBLE
            swipeRefreshLayout?.isRefreshing = false
        }
        
        fun hideLoading() {
            progressBar?.visibility = View.GONE
            swipeRefreshLayout?.isRefreshing = false
        }
    }
    
    /**
     * Debounce utility for search and other frequent operations
     */
    class Debouncer(
        private val delayMs: Long = 300L,
        private val scope: CoroutineScope = CoroutineScope(Dispatchers.Main)
    ) {
        private var debounceJob: Job? = null
        
        fun debounce(action: suspend () -> Unit) {
            debounceJob?.cancel()
            debounceJob = scope.launch {
                delay(delayMs)
                action()
            }
        }
        
        fun cancel() {
            debounceJob?.cancel()
        }
    }
    
    /**
     * Memory usage monitoring
     */
    fun logMemoryUsage(context: Context, tag: String = "MemoryUsage") {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory
        
        Log.d(tag, "Memory Usage - Used: ${usedMemory / 1024 / 1024}MB, " +
                "Available: ${availableMemory / 1024 / 1024}MB, " +
                "Max: ${maxMemory / 1024 / 1024}MB")
    }
    
    /**
     * RecyclerView optimization utilities
     */
    object RecyclerViewOptimizer {
        
        /**
         * Optimize RecyclerView for better performance
         */
        fun optimizeRecyclerView(recyclerView: RecyclerView) {
            recyclerView.apply {
                setHasFixedSize(true)
                setItemViewCacheSize(20)
                isDrawingCacheEnabled = true
                drawingCacheQuality = View.DRAWING_CACHE_QUALITY_HIGH
            }
            Log.d(TAG, "Optimized RecyclerView performance settings")
        }
        
        /**
         * Add scroll listener for pagination
         */
        fun addPaginationScrollListener(
            recyclerView: RecyclerView,
            layoutManager: androidx.recyclerview.widget.LinearLayoutManager,
            onLoadMore: () -> Unit
        ) {
            recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    
                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
                    
                    // Load more when reaching the last 5 items
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                        onLoadMore()
                    }
                }
            })
            Log.d(TAG, "Added pagination scroll listener")
        }
    }
    
    /**
     * Background task utilities
     */
    object BackgroundTaskUtils {
        
        /**
         * Execute task in background with loading state
         */
        fun <T> executeWithLoading(
            loadingStateManager: LoadingStateManager,
            backgroundTask: suspend () -> T,
            onSuccess: (T) -> Unit,
            onError: (Exception) -> Unit = { Log.e(TAG, "Background task failed", it) }
        ) {
            loadingStateManager.showLoading()
            
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val result = backgroundTask()
                    withContext(Dispatchers.Main) {
                        loadingStateManager.hideLoading()
                        onSuccess(result)
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        loadingStateManager.hideLoading()
                        onError(e)
                    }
                }
            }
        }
        
        /**
         * Execute multiple tasks concurrently
         */
        fun <T> executeConcurrently(
            tasks: List<suspend () -> T>,
            onAllComplete: (List<T>) -> Unit,
            onError: (Exception) -> Unit = { Log.e(TAG, "Concurrent tasks failed", it) }
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val results = tasks.map { task ->
                        async { task() }
                    }.awaitAll()
                    
                    withContext(Dispatchers.Main) {
                        onAllComplete(results)
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        onError(e)
                    }
                }
            }
        }
    }
    
    /**
     * Data prefetching utilities
     */
    object PrefetchUtils {
        
        /**
         * Prefetch data in background
         */
        fun prefetchData(
            cacheManager: CacheManager,
            prefetchOperations: List<suspend () -> Unit>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                prefetchOperations.forEach { operation ->
                    try {
                        operation()
                    } catch (e: Exception) {
                        Log.e(TAG, "Prefetch operation failed", e)
                    }
                }
                Log.d(TAG, "Completed ${prefetchOperations.size} prefetch operations")
            }
        }
        
        /**
         * Prefetch images for better UX
         */
        fun prefetchImages(context: Context, imageUrls: List<String>) {
            CoroutineScope(Dispatchers.IO).launch {
                delay(1000) // Wait a bit before prefetching
                withContext(Dispatchers.Main) {
                    ImageUtils.preloadImages(context, imageUrls)
                }
            }
        }
    }
    
    /**
     * UI performance utilities
     */
    object UIPerformanceUtils {
        
        /**
         * Delay execution to next frame for better UI performance
         */
        fun runOnNextFrame(action: () -> Unit) {
            Handler(Looper.getMainLooper()).post(action)
        }
        
        /**
         * Batch UI updates for better performance
         */
        fun batchUIUpdates(updates: List<() -> Unit>) {
            Handler(Looper.getMainLooper()).post {
                updates.forEach { it() }
            }
        }
        
        /**
         * Smooth scroll to position with delay
         */
        fun smoothScrollWithDelay(
            recyclerView: RecyclerView,
            position: Int,
            delayMs: Long = 100L
        ) {
            Handler(Looper.getMainLooper()).postDelayed({
                recyclerView.smoothScrollToPosition(position)
            }, delayMs)
        }
    }
    
    /**
     * Network performance utilities
     */
    object NetworkUtils {
        
        /**
         * Retry mechanism for network operations
         */
        suspend fun <T> retryOperation(
            maxRetries: Int = 3,
            delayMs: Long = 1000L,
            operation: suspend () -> T
        ): T {
            repeat(maxRetries - 1) { attempt ->
                try {
                    return operation()
                } catch (e: Exception) {
                    Log.w(TAG, "Operation failed, attempt ${attempt + 1}/$maxRetries", e)
                    delay(delayMs * (attempt + 1)) // Exponential backoff
                }
            }
            return operation() // Last attempt without catch
        }
    }
}
