package com.example.jobrec.utils

import android.util.Log
import com.google.firebase.firestore.*
import kotlinx.coroutines.tasks.await

/**
 * Generic pagination helper for Firestore queries
 * Provides efficient pagination with caching and loading states
 */
class PaginationHelper<T>(
    private val pageSize: Int = 20,
    private val cacheKey: String,
    private val cacheManager: CacheManager,
    private val documentMapper: (QueryDocumentSnapshot) -> T?
) {
    
    companion object {
        private const val TAG = "PaginationHelper"
    }
    
    private var lastDocument: DocumentSnapshot? = null
    private var isLoading = false
    private var hasMoreData = true
    private var currentPage = 0
    private val allItems = mutableListOf<T>()
    
    data class PaginationResult<T>(
        val items: List<T>,
        val isFirstPage: Boolean,
        val hasMore: Boolean,
        val totalLoaded: Int
    )
    
    /**
     * Load the first page of data
     */
    suspend fun loadFirstPage(query: Query): PaginationResult<T> {
        Log.d(TAG, "Loading first page for cache key: $cacheKey")
        
        // Try to get cached data first
        val cachedItems = cacheManager.getJobsList(cacheKey) as? List<T>
        if (cachedItems != null && cachedItems.isNotEmpty()) {
            Log.d(TAG, "Using cached data: ${cachedItems.size} items")
            allItems.clear()
            allItems.addAll(cachedItems)
            
            // Take only the first page from cache
            val firstPageItems = cachedItems.take(pageSize)
            return PaginationResult(
                items = firstPageItems,
                isFirstPage = true,
                hasMore = cachedItems.size > pageSize,
                totalLoaded = firstPageItems.size
            )
        }
        
        // Load fresh data from Firestore
        return loadPage(query, isFirstPage = true)
    }
    
    /**
     * Load the next page of data
     */
    suspend fun loadNextPage(query: Query): PaginationResult<T> {
        if (isLoading || !hasMoreData) {
            Log.d(TAG, "Cannot load next page - loading: $isLoading, hasMore: $hasMoreData")
            return PaginationResult(
                items = emptyList(),
                isFirstPage = false,
                hasMore = hasMoreData,
                totalLoaded = allItems.size
            )
        }
        
        return loadPage(query, isFirstPage = false)
    }
    
    /**
     * Load a page of data from Firestore
     */
    private suspend fun loadPage(query: Query, isFirstPage: Boolean): PaginationResult<T> {
        if (isLoading) {
            Log.d(TAG, "Already loading, skipping request")
            return PaginationResult(
                items = emptyList(),
                isFirstPage = isFirstPage,
                hasMore = hasMoreData,
                totalLoaded = allItems.size
            )
        }
        
        isLoading = true
        
        try {
            Log.d(TAG, "Loading page ${currentPage + 1} for cache key: $cacheKey")
            
            val paginatedQuery = if (isFirstPage || lastDocument == null) {
                // Reset pagination for first page
                lastDocument = null
                currentPage = 0
                allItems.clear()
                query.limit(pageSize.toLong())
            } else {
                // Continue from last document
                query.startAfter(lastDocument!!).limit(pageSize.toLong())
            }
            
            val querySnapshot = paginatedQuery.get().await()
            val documents = querySnapshot.documents
            
            Log.d(TAG, "Received ${documents.size} documents from Firestore")
            
            val newItems = documents.mapNotNull { doc ->
                try {
                    documentMapper(doc as QueryDocumentSnapshot)
                } catch (e: Exception) {
                    Log.e(TAG, "Error mapping document ${doc.id}", e)
                    null
                }
            }
            
            // Update pagination state
            lastDocument = documents.lastOrNull()
            hasMoreData = documents.size == pageSize
            currentPage++
            
            // Add to all items list
            allItems.addAll(newItems)
            
            // Cache the updated list if it's reasonable size (first few pages)
            if (allItems.size <= pageSize * 3) {
                try {
                    when (cacheKey.contains("jobs")) {
                        true -> cacheManager.cacheJobsList(cacheKey, allItems as List<com.example.jobrec.Job>)
                        false -> cacheManager.cacheApplicationsList(cacheKey, allItems as List<com.example.jobrec.Application>)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error caching items", e)
                }
            }
            
            Log.d(TAG, "Loaded ${newItems.size} new items. Total: ${allItems.size}, HasMore: $hasMoreData")
            
            return PaginationResult(
                items = newItems,
                isFirstPage = isFirstPage,
                hasMore = hasMoreData,
                totalLoaded = allItems.size
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading page", e)
            hasMoreData = false
            return PaginationResult(
                items = emptyList(),
                isFirstPage = isFirstPage,
                hasMore = false,
                totalLoaded = allItems.size
            )
        } finally {
            isLoading = false
        }
    }
    
    /**
     * Refresh data by clearing cache and reloading first page
     */
    suspend fun refresh(query: Query): PaginationResult<T> {
        Log.d(TAG, "Refreshing data for cache key: $cacheKey")
        
        // Clear cache
        if (cacheKey.contains("jobs")) {
            cacheManager.invalidateJobsCache()
        } else {
            cacheManager.invalidateApplicationsCache()
        }
        
        // Reset pagination state
        reset()
        
        // Load fresh data
        return loadPage(query, isFirstPage = true)
    }
    
    /**
     * Reset pagination state
     */
    fun reset() {
        lastDocument = null
        isLoading = false
        hasMoreData = true
        currentPage = 0
        allItems.clear()
        Log.d(TAG, "Reset pagination state for cache key: $cacheKey")
    }
    
    /**
     * Get current loading state
     */
    fun isCurrentlyLoading(): Boolean = isLoading
    
    /**
     * Check if more data is available
     */
    fun hasMoreData(): Boolean = hasMoreData
    
    /**
     * Get total number of loaded items
     */
    fun getTotalLoadedItems(): Int = allItems.size
    
    /**
     * Get all currently loaded items
     */
    fun getAllLoadedItems(): List<T> = allItems.toList()
    
    /**
     * Search within loaded items
     */
    fun searchLoadedItems(searchTerm: String, searchFunction: (T, String) -> Boolean): List<T> {
        return allItems.filter { item ->
            searchFunction(item, searchTerm.lowercase())
        }
    }
}

/**
 * Factory methods for creating pagination helpers
 */
object PaginationHelperFactory {
    
    fun createJobsPaginationHelper(
        pageSize: Int = 20,
        cacheKey: String,
        cacheManager: CacheManager
    ): PaginationHelper<com.example.jobrec.Job> {
        return PaginationHelper(
            pageSize = pageSize,
            cacheKey = cacheKey,
            cacheManager = cacheManager
        ) { doc ->
            try {
                val job = doc.toObject(com.example.jobrec.Job::class.java)
                job.id = doc.id
                job
            } catch (e: Exception) {
                Log.e("PaginationHelperFactory", "Error mapping job document", e)
                null
            }
        }
    }
    
    fun createApplicationsPaginationHelper(
        pageSize: Int = 20,
        cacheKey: String,
        cacheManager: CacheManager
    ): PaginationHelper<com.example.jobrec.Application> {
        return PaginationHelper(
            pageSize = pageSize,
            cacheKey = cacheKey,
            cacheManager = cacheManager
        ) { doc ->
            try {
                val application = doc.toObject(com.example.jobrec.Application::class.java)
                application.id = doc.id
                application
            } catch (e: Exception) {
                Log.e("PaginationHelperFactory", "Error mapping application document", e)
                null
            }
        }
    }
}
