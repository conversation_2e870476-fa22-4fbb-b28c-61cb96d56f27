package com.example.jobrec

import com.example.jobrec.utils.PerformanceUtils
import org.junit.Test
import org.junit.Assert.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.delay

/**
 * Test class to verify performance optimization utilities
 */
class PerformanceOptimizationTest {

    @Test
    fun testPerformanceTimer() {
        val timer = PerformanceUtils.startTimer("Test Operation")
        
        // Simulate some work
        Thread.sleep(100)
        
        val duration = timer.end()
        
        // Verify timer measured some duration
        assertTrue("Timer should measure positive duration", duration > 0)
        assertTrue("Timer should measure at least 100ms", duration >= 100)
    }

    @Test
    fun testDebouncer() = runBlocking {
        val debouncer = PerformanceUtils.Debouncer(100L)
        var executionCount = 0
        
        // Trigger debouncer multiple times quickly
        repeat(5) {
            debouncer.debounce {
                executionCount++
            }
            delay(20) // Small delay between calls
        }
        
        // Wait for debounce period to complete
        delay(150)
        
        // Should only execute once due to debouncing
        assertEquals("Debouncer should execute only once", 1, executionCount)
    }

    @Test
    fun testImageUtilsCompression() {
        // This would require Android context, so we'll test the logic
        val quality = 85
        assertTrue("Compression quality should be valid", quality in 1..100)
        
        val maxSize = 1024
        assertTrue("Max image size should be positive", maxSize > 0)
    }

    @Test
    fun testCacheKeyGeneration() {
        val userId = "test_user_123"
        val status = "PENDING"
        val cacheKey = "user_applications_${userId}_$status"
        
        assertEquals("Cache key should be properly formatted", 
                    "user_applications_test_user_123_PENDING", cacheKey)
    }

    @Test
    fun testPaginationLogic() {
        val pageSize = 20
        val totalItems = 45
        
        val totalPages = (totalItems + pageSize - 1) / pageSize
        assertEquals("Should calculate correct number of pages", 3, totalPages)
        
        val page1Start = 0
        val page1End = minOf(pageSize, totalItems)
        assertEquals("Page 1 should start at 0", 0, page1Start)
        assertEquals("Page 1 should end at 20", 20, page1End)
        
        val page3Start = 2 * pageSize
        val page3End = minOf(3 * pageSize, totalItems)
        assertEquals("Page 3 should start at 40", 40, page3Start)
        assertEquals("Page 3 should end at 45", 45, page3End)
    }

    @Test
    fun testMemoryOptimization() {
        // Test that we're using appropriate data structures
        val concurrentMap = java.util.concurrent.ConcurrentHashMap<String, String>()
        concurrentMap["test"] = "value"
        
        assertTrue("ConcurrentHashMap should be thread-safe", 
                  concurrentMap is java.util.concurrent.ConcurrentHashMap)
        assertEquals("Should store and retrieve values correctly", "value", concurrentMap["test"])
    }

    @Test
    fun testCacheExpiration() {
        val cacheTimestamp = System.currentTimeMillis() - 6 * 60 * 1000 // 6 minutes ago
        val expiryTime = 5 * 60 * 1000L // 5 minutes
        val currentTime = System.currentTimeMillis()
        
        val isExpired = (currentTime - cacheTimestamp) > expiryTime
        assertTrue("Cache should be expired after 6 minutes", isExpired)
        
        val recentTimestamp = System.currentTimeMillis() - 2 * 60 * 1000 // 2 minutes ago
        val isRecentExpired = (currentTime - recentTimestamp) > expiryTime
        assertFalse("Cache should not be expired after 2 minutes", isRecentExpired)
    }

    @Test
    fun testLoadingStateTransitions() {
        // Test loading state logic
        var isLoading = false
        var showContent = false
        var showEmpty = false
        
        // Simulate showing loading
        isLoading = true
        showContent = false
        showEmpty = false
        
        assertTrue("Should show loading state", isLoading)
        assertFalse("Should not show content during loading", showContent)
        assertFalse("Should not show empty during loading", showEmpty)
        
        // Simulate showing content
        isLoading = false
        showContent = true
        showEmpty = false
        
        assertFalse("Should not show loading when content is ready", isLoading)
        assertTrue("Should show content when data is available", showContent)
        assertFalse("Should not show empty when content is available", showEmpty)
    }

    @Test
    fun testSearchDebouncing() {
        // Test search query optimization
        val query1 = "software"
        val query2 = "software"
        val query3 = "software engineer"
        
        // Same queries should not trigger new search
        assertEquals("Same queries should be equal", query1, query2)
        assertNotEquals("Different queries should not be equal", query1, query3)
    }

    @Test
    fun testImageSizeOptimization() {
        val originalWidth = 2048
        val originalHeight = 1536
        val maxSize = 1024
        
        val aspectRatio = originalWidth.toFloat() / originalHeight.toFloat()
        
        val newWidth: Int
        val newHeight: Int
        
        if (originalWidth > originalHeight) {
            newWidth = maxSize
            newHeight = (maxSize / aspectRatio).toInt()
        } else {
            newHeight = maxSize
            newWidth = (maxSize * aspectRatio).toInt()
        }
        
        assertTrue("New width should not exceed max size", newWidth <= maxSize)
        assertTrue("New height should not exceed max size", newHeight <= maxSize)
        assertTrue("Should maintain aspect ratio", 
                  kotlin.math.abs(newWidth.toFloat() / newHeight.toFloat() - aspectRatio) < 0.01f)
    }
}
